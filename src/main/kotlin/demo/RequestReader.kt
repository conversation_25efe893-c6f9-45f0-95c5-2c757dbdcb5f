package demo

import demo.meta.Request
import demo.parser.HeadersParser
import demo.parser.QueryParser
import demo.parser.RequestLineParser
import java.io.InputStream

class RequestReader(private val input: InputStream) {

    fun read(): Request = with(input.bufferedReader()) {
        val (method, target) = readLine().let(RequestLineParser::parse)
        val queries = QueryParser.parse(target.query)

        val requestHeaders = lineSequence()
            .takeWhile { it.isNotEmpty() }
            .toList()
            .let(HeadersParser::parse)

        val contentLength = requestHeaders.get("Content-Length")
        val buffer = CharArray(contentLength.toInt())
        read(buffer)
        val requestBody = String(buffer)

        return Request(
            method = method,
            path = target.path,
            queries = queries,
            headers = requestHeaders,
            body = requestBody,
        )
    }
}
