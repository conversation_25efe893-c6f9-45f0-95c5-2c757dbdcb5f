package demo

import demo.meta.Request

class Router {
    private val routes = mutableMapOf<RequestMatcher, RequestHandler>()

    fun route(request: Request): RequestHandler {
        for ((matcher, handler) in routes) {
            if (matcher.match(request)) return handler
        }
        return RequestHandler.NOT_FOUND
    }

    fun registerHandler(matcher: <PERSON>questMatcher, handler: <PERSON>questHandler) {
        routes[matcher] = handler
    }
}