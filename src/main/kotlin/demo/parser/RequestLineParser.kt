package demo.parser

import demo.meta.Method
import demo.meta.RequestLine
import java.net.URI

object RequestLineParser {

    fun parse(requestLine: String): RequestLine {
        val parts = requestLine.split(" ", limit = 3)
        val method = parts[0]
        val path = parts[1]
        return RequestLine(
            method = Method.valueOf(method),
            target = URI(path),
        )
    }
}
