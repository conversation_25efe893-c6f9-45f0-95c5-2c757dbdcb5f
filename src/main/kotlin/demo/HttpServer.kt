package demo

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import demo.meta.Headers
import demo.meta.Method
import demo.meta.Response
import java.io.Closeable
import java.net.ServerSocket
import java.net.Socket
import java.util.concurrent.Executors

data class User(
    val name: String,
    val email: String,
)

fun main() {
    val server = HttpServer(8080)
    server.post("/users", fun(user: User): Int {
        println("Created user: $user")
        return 1
    })
    server.use(HttpServer::start)
}

class HttpServer(port: Int) : Closeable {
    private val serverSocket = ServerSocket(port)
    private val executor = Executors.newVirtualThreadPerTaskExecutor()
    private val router = Router()
    val mapper = jacksonObjectMapper()

    fun start() {
        while (true) {
            val socket = serverSocket.accept()
            val handler = SocketHandler(socket, router)
            executor.execute(handler)
        }
    }

    fun registerHandler(matcher: RequestMatcher, handler: Request<PERSON>andler) = apply {
        router.registerHandler(matcher, handler)
    }

    fun registerHandler(method: Method, path: String, handler: RequestHandler) = registerHandler(RequestMatcher.on(method, path), handler)

    inline fun <reified Input> post(path: String, crossinline block: (Input) -> Any?) = registerHandler(Method.POST, path) { request ->
        val input = mapper.readValue<Input>(request.body)
        val output = block(input)
        val responseBody = mapper.writeValueAsString(output)
        Response(
            body = responseBody,
            headers = Headers(emptyMap()),
            status = 200,
        )
    }

    override fun close() {
        serverSocket.close()
        executor.close()
    }
}

class SocketHandler(
    private val socket: Socket,
    private val router: Router,
) : Runnable {

    override fun run() = socket.use {
        val reader = RequestReader(socket.getInputStream())
        val request = reader.read()
        val requestHandler = router.route(request)
        val response = requestHandler.handle(request)
        val writer = ResponseWriter(socket.getOutputStream())
        writer.write(response)
    }
}
